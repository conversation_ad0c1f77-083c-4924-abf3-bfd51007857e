import Joi from 'joi';

const configSchema = Joi.object()
  .keys({
    KAFKA_BROKERS: Joi.string().required(),
    KAFKA_USERNAME: Joi.string().required(),
    KAFKA_PASSWORD: Joi.string().required(),
    KAFKA_SECURITY_PROTOCOL: Joi.string().required(),
    KAFKA_CONNECTION_TIMEOUT: Joi.number().default(30000),
    KAFKA_TOPIC_EMAIL: Joi.string(),
  })
  .unknown(true);

const { error } = configSchema.validate(process.env);
if (error) throw new Error(`${error.message}`);

const kafkaConfig = {
  KAFKA_BROKERS: process.env.KAFKA_BROKERS.split(','),
  KAFKA_USERNAME: process.env.KAFKA_USERNAME,
  KAFKA_PASSWORD: process.env.KAFKA_PASSWORD,
  KAFKA_SECURITY_PROTOCOL: process.env.KAFKA_SECURITY_PROTOCOL,
  CONNECTION_TIMEOUT: Number.parseInt(process.env.KAFKA_CONNECTION_TIMEOUT) || 30000,
  KAFKA_TOPIC_EMAIL: process.env.KAFKA_TOPIC_EMAIL || 'SmartAlert.Notification.Email',
};

export default kafkaConfig;
