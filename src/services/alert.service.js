import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import kafkaConnection from '../connections/kafka.js';
import { kafkaConfig } from '../config/index.js';
import { alertRepository } from '../repositories/alert.repository.js';
import { withLock } from './lock.service.js';
import { siteService } from './site.service.js';
import { userService } from './user.service.js';
import logger from '../utils/logger.js';

class AlertService {
  async checkAndEscalateAlerts() {
    logger.info('Starting alert escalation check');
    const now = moment().tz('UTC').toISOString();
    let incidentsToEscalate;

    try {
      incidentsToEscalate = await alertRepository.getIncidentsToEscalate();
      logger.info(`Found ${incidentsToEscalate.length} incidents that need escalation`);
    } catch (error) {
      logger.error(`Failed to fetch incidents to escalate: ${error.message}`, { error });
      throw error;
    }

    for (const incident of incidentsToEscalate) {
      const logContext = {
        incidentId: incident.id,
        alertInventoryId: incident.alert_inventory_id,
        siteId: incident.siteid,
      };

      logger.info(`Processing incident for escalation`, logContext);

      const lockKey = `alert:escalation_lock:${incident.id}`;
      try {
        await withLock(lockKey, async () => {
          await alertRepository.updateIncidentEscalation(incident.id, now);

          const notification = await this.prepareEscalationNotification(incident);

          await this.sendEscalationNotification(notification);
        });
      } catch (error) {
        logger.error(`Failed to process incident ${incident.id} for escalation: ${error.message}`, {
          ...logContext,
          error,
        });
      }
    }
  }

  async prepareEscalationNotification(incident) {
    const siteName = await siteService.getSiteNameBySiteId(incident.siteid);
    const transactionId = `escalation-trigger-service_${uuidv4()}`;

    logger.debug('Preparing escalation notification', {
      incidentId: incident.id,
      siteId: incident.siteid,
      transactionId,
    });

    // Try to get title and description from notification_message_template
    let title = incident.name;
    let body = incident.description;

    try {
      const template = await alertRepository.getNotificationMessageTemplate(
        incident.alert_inventory_id,
        incident.alert_template_ref_id,
        'email',
        'OCCURRED'
      );

      if (template) {
        title = template.title || title;
        body = template.description || body;
      }
    } catch (error) {
      logger.warn(`Failed to fetch notification template, using default title/description: ${error.message}`, {
        error,
        incidentId: incident.id
      });
    }

    const recipients = await userService.getRecipientsFromEmailList(incident.escalated_to);
    if (recipients.length === 0) {
      logger.warn(`No valid recipients found for incident ${incident.id}`);
    }

    return {
      channel: 'email',
      recipients,
      content: {
        title,
        body,
      },
      metadata: {
        incidentId: incident.id,
        alertInventoryId: incident.alert_inventory_id,
        siteId: incident.siteid,
        siteName,
        eventName: 'ESCALATED',
        severity: incident.severity,
        alertType: incident.alert_category || '',
        deviceId: incident.asset_id || '',
        deviceType: incident.asset_type || '',
        assetName: incident.name,
        observer_execution_ref_id: incident.observer_execution_ref_id || '',
        timestamp: moment(incident.issue_occurred_at).tz('UTC').toISOString(),
        timestampOccurred: null,
      },
      ts: moment().tz('UTC').toISOString(),
      transactionId,
    };
  }

  async sendEscalationNotification(notification) {
    try {
      await kafkaConnection.sendMessage(
        kafkaConfig.KAFKA_TOPIC_EMAIL,
        notification
      );

      logger.info('🚀 Alert escalation complete', {
        notification
      });
    } catch (error) {
      logger.error(`🤡 Failed to publish alert escalation notification: ${error.message}`, {
        error,
        transactionId: notification.transactionId,
        recipientEmails: notification.recipients.map(r => r.name)
      });
      throw error;
    }
  }
}

export const alertService = new AlertService();
