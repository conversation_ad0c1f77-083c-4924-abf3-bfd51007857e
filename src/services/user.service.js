import redisClient from '../connections/redis.js';
import { dynamoDbClient } from '../connections/dynamodb.js';
import logger from '../utils/logger.js';

// Random TTL between 23-24 hours to prevent cache stampede
const getRandomDayTTL = () => Math.floor(23 * 60 * 60 + Math.random() * 60 * 60);

class UserService {
  async getRecipientByEmail(email) {
    if (!email || typeof email !== 'string') {
      return null;
    }

    const cacheKey = `alert:user:email:${email}`;

    try {
      // Try to get from cache first
      const cachedUser = await redisClient.get(cacheKey);
      if (cachedUser) {
        logger.debug(`User found in cache for email: ${email}`);
        return JSON.parse(cachedUser);
      }

      // If not in cache, get from DynamoDB
      const userData = await dynamoDbClient.getUserByEmail(email);
      const userName = userData?.name || email.split('@')[0];

      // Create recipient object
      const recipient = {
        name: userName,
        email
      };

      // Cache with TTL
      const ttl = getRandomDayTTL();
      await redisClient.set(cacheKey, JSON.stringify(recipient));
      await redisClient.expire(cacheKey, ttl);

      if (userData) {
        logger.debug(`User fetched from DynamoDB for email: ${email}`);
      } else {
        logger.debug(`No user found in DynamoDB for email: ${email}, using derived name`);
      }

      return recipient;
    } catch (error) {
      logger.error(`Failed to get user for email ${email}: ${error.message}`, { error });
      // Fallback to simple name extraction
      return {
        name: email.split('@')[0],
        email
      };
    }
  }

  async getRecipientsFromEmailList(emailList) {
    if (!emailList || !Array.isArray(emailList)) {
      return [];
    }

    const recipientPromises = emailList.map(email => this.getRecipientByEmail(email));
    const recipients = (await Promise.all(recipientPromises)).filter(Boolean);

    return recipients;
  }
}

export const userService = new UserService();
