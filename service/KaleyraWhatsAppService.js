import axios from 'axios';
import moment from 'moment-timezone';
import FormData from 'form-data';
import logger from '../utils/logger.js';

export default class KaleyraWhatsAppService {
  constructor() {
    this.apiKey = process.env.KALEYRA_API_KEY;
    this.sid = process.env.KALEYRA_SID;
    this.fromNumber = process.env.KALEYRA_WHATSAPP_NUMBER;
    this.baseUrl =
      process.env.KALEYRA_API_BASE_URL || 'https://api.kaleyra.io/v1';

    if (!this.apiKey || !this.sid || !this.fromNumber) {
      throw new Error(
        'Missing required Kaleyra configuration. Please check environment variables.',
      );
    }

    // Rate limiting configuration
    this.rateLimitPerSecond =
      parseInt(process.env.WHATSAPP_RATE_LIMIT_PER_SECOND) || 50;
    this.batchSize = parseInt(process.env.WHATSAPP_BATCH_SIZE) || 100;
  }

  /**
   * Replace variables in title and body with actual values
   */
  replaceVariables(text, metadata) {
    if (!text) return text;

    const isOccurredEvent = metadata.eventName.toLowerCase() === 'occurred';
    const timestampForOccurrence = isOccurredEvent
      ? metadata.timestamp
      : metadata.timestampOccurred;

    const replacements = {
      '{{AssetName}}': metadata.assetName,
      AssetName: metadata.assetName,
      SiteName: metadata.siteName,
      AssetType: metadata.deviceType,
      Timestamp: this.formatTimestamp(metadata.timestamp),
      TimestampResolved: this.formatTimestamp(metadata.timestamp),
      TimestampOccurred: this.formatTimestamp(timestampForOccurrence),
      OcurredTimestamp: this.formatTimestamp(timestampForOccurrence),
      ResolvedTimestamp: this.formatTimestamp(metadata.timestamp),
    };
    let result = text;
    Object.entries(replacements).forEach(([variable, value]) => {
      result = result.replace(new RegExp(variable, 'g'), value);
    });

    // Remove line breaks and newlines for Kaleyra compatibility
    result = result
      .replace(/\r\n|\r|\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    return result;
  }
  /**
   * Send WhatsApp notification using template message
   * @param {Object} notificationData - The notification data from Kafka message
   * @returns {Promise<Object>} - Response with send results
   */
  async sendNotification(notificationData) {
    const { recipients, content, metadata } = notificationData;

    try {
      // Process recipients in batches to respect rate limits
      const batches = this.createBatches(recipients, this.batchSize);
      const allResults = [];

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const batchResults = await Promise.allSettled(
          batch.map((recipient) =>
            this.sendToRecipient(recipient, content, metadata),
          ),
        );

        allResults.push(...batchResults);

        // Add delay between batches to respect rate limit
        if (i < batches.length - 1) {
          await this.delay(1000);
        }
      }

      const successCount = allResults.filter(
        (r) => r.status === 'fulfilled',
      ).length;
      const failureCount = allResults.filter(
        (r) => r.status === 'rejected',
      ).length;

      logger.info(`WhatsApp notification batch completed`, {
        notificationData,
        total: recipients.length,
        success: successCount,
        failed: failureCount,
      });
    } catch (error) {
      logger.error(`Failed to process WhatsApp notification`, {
        notificationData,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Get emoji based on event name
   */
  getEventEmoji(eventName) {
    const eventLower = eventName.toLowerCase();

    const emojiMap = {
      occurred: '🚨',
      resolved: '✅',
      reminder: '⏰',
      escalated: '⬆️',
    };

    return emojiMap[eventLower] || '📢';
  }

  /**
   * Send WhatsApp message to individual recipient
   */
  async sendToRecipient(recipient, content, metadata) {
    try {
      const whatsappNumber = this.formatPhoneNumber(
        recipient.whatsappNumber || recipient.mobileNumber,
      );

      if (!whatsappNumber) {
        throw new Error(
          `No valid WhatsApp number for recipient ${recipient.email}`,
        );
      }

      // Updated template names
      const templateNameMap = {
        occurred: 'test_occurence_template',
        resolved: 'test_resolvedp_template',
        reminder: 'test_reminderp_template',
      };

      const templateName = templateNameMap[metadata.eventName.toLowerCase()] || 'test_occurence_template';

      const processedTitle = this.replaceVariables(content.title, metadata);
      const processedBody = this.replaceVariables(content.body, metadata);

      const emoji = this.getEventEmoji(metadata.eventName);
      const titleWithEmoji = `${emoji} ${processedTitle}`;

      const finalProcessedBody =
        recipient.email === '<EMAIL>'
          ? `${processedBody}🍃🍃🍃🍃🍃🍃🍃🍃🍃🍃`
          : processedBody;

      const templateParams = [
        metadata.alertType.toUpperCase(),
        titleWithEmoji,
        metadata.alertInventoryId,
        recipient.name,
        metadata.severity.toUpperCase(),
        metadata.deviceType,
        metadata.assetName,
        metadata.siteName,
        this.formatTimestamp(metadata.timestamp),
        finalProcessedBody,
      ];

      // Add timestampOccurred if it exists (for occurred events)
      if (metadata.timestampOccurred) {
        templateParams.push(this.formatTimestamp(metadata.timestampOccurred)); // 11. timestampOccurred
      }

      const payload = {
        from: this.fromNumber,
        to: whatsappNumber,
        type: 'mediatemplate',
        channel: 'whatsapp',
        template_name: templateName,
        params: templateParams.map((param) => `"${param}"`).join(','),
        param_url: `${metadata.siteId}/alerts/${metadata.alertInventoryId}`,
        param_header: `"${metadata.alertInventoryId}"`,
      };

      // Make API call
      const response = await this.makeApiCall('POST', '/messages', payload);

      if (!response.data || !response.data.id || !response.data.data) {
        throw new Error('Invalid response structure from Kaleyra API');
      }

      const messageId = response.data.data[0]?.message_id || response.data.id;

      logger.info(`WhatsApp message sent successfully`, {
        messageId,
        recipient: recipient.email,
        whatsappNumber,
      });

      return {
        recipient: recipient.email,
        whatsappNumber,
        messageId,
        status: 'sent',
      };
    } catch (error) {
      logger.error(`Failed to send WhatsApp message`, {
        recipient: recipient.email,
        whatsappNumber: recipient.whatsappNumber,
        error: error.message,
        errorDetails: error.response?.data,
      });

      throw error;
    }
  }

  /**
   * Format phone number to E.164 format required by WhatsApp
   */
  formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return null;

    // Remove all non-numeric characters
    let cleaned = phoneNumber.toString().replace(/\D/g, '');

    // Remove leading zeros
    cleaned = cleaned.replace(/^0+/, '');

    // Add country code if not present (defaulting to India +91)
    if (cleaned.length === 10) {
      cleaned = '91' + cleaned;
    }

    // Add + prefix
    return '+' + cleaned;
  }

  /**
   * Format timestamp for display in IST
   */
  formatTimestamp(timestamp) {
    try {
      return moment(timestamp)
        .tz('Asia/Kolkata')
        .format('DD MMM YYYY hh:mm:ss A');
    } catch (error) {
      logger.warn(`Failed to format timestamp: ${error.message}`, {
        timestamp,
      });
      return timestamp;
    }
  }

  /**
   * Make API call to Kaleyra
   */
  async makeApiCall(method, endpoint, data) {
    const url = `${this.baseUrl}/${this.sid}${endpoint}`;

    try {
      const config = {
        method,
        url,
        headers: {
          'api-key': this.apiKey,
        },
        timeout: 30000, // 30 second timeout
        validateStatus: (status) => status === 200 || status === 202,
      };

      if (method === 'POST') {
        const formData = new FormData();

        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });

        config.data = formData;
      } else if (method === 'GET') {
        config.params = data;
      }

      logger.debug(`📡 Kaleyra API call`, {
        method,
        endpoint,
        templateName: data.template_name,
      });

      const response = await axios(config);

      return response;
    } catch (error) {
      if (error.response) {
        const errorData = error.response.data;
        if (errorData?.error?.message) error.message = errorData.error.message;
        else if (errorData?.message) error.message = errorData.message;
        else if (errorData?.error?.type) error.message = errorData.error.type;
        else if (typeof errorData === 'string') error.message = errorData;
      }

      throw error;
    }
  }

  /**
   * Create batches of recipients
   */
  createBatches(recipients, batchSize) {
    const batches = [];
    for (let i = 0; i < recipients.length; i += batchSize) {
      batches.push(recipients.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Helper function for delay
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
